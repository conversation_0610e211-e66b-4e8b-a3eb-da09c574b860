# Archive

This is an archive for formalizations which don't have a good place in mathlib, probably because there is (almost) no math depending on these results.

We keep these formalizations here so that when mathlib changes, we can keep these formalizations up to date.

## Contributing

If you have done a formalization which you want to add here, just make a pull request to mathlib.

When you make a pull request, do make sure that you put all lemmas which are generally useful in right place in mathlib (in the `Mathlib/` directory).

Try to adhere to the guidelines for mathlib. They will be much less strictly enforced for the archive, but we still want you to adhere to all the conventions that make maintenance easier. This ensures that when mathlib is changing, the mathlib maintainers can fix these contributions without much effort. Here are the guidelines:
- The [style guide](https://leanprover-community.github.io/contribute/style.html) for contributors.
- The [git commit conventions](https://github.com/leanprover/lean4/blob/master/doc/dev/commit_convention.md).
- You don't have to follow the [naming conventions](https://leanprover-community.github.io/contribute/naming.html).
