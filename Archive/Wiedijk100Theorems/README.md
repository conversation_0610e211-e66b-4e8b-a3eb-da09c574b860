# "Formalizing 100 Theorems" in Lean

In this folder, we keep proofs of theorems on <PERSON><PERSON>'s [100 theorems list](https://www.cs.ru.nl/~freek/100/) which don't fit naturally elsewhere in mathlib or in other Lean repositories.

See [this page](https://leanprover-community.github.io/100.html) for more information about theorems from the list above which have been formalized in Lean. If you prove a new theorem from that list, you should add the appropriate data to this file:
https://github.com/leanprover-community/mathlib4/blob/master/docs/100.yaml
