name: Check workflows
on:
  pull_request:
    paths:
      - '.github/**'
  merge_group:

jobs:
  actionlint:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: suggester / actionlint
        uses: reviewdog/action-actionlint@a5524e1c19e62881d79c1f1b9b6f09f16356e281 # v1.65.2
        with:
          tool_name: actionlint
          fail_level: error

  check_build_yml:
    name: check workflows generated by build.in.yml
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: update workflows
        run: |
          cd .github/workflows/
          ./mk_build_yml.sh

      - name: suggester / build.in.yml
        uses: reviewdog/action-suggester@4747dbc9f9e37adba0943e681cc20db466642158 # v1.21.0
        with:
          tool_name: mk_build_yml.sh
          fail_level: error
