name: Cross off linked issues

on:
  # the closed event type causes unchecked checkbox references to be checked / marked complete
  # the reopened event type causes checked checkbox references to be unchecked / marked incomplete
  issues:
    types: [closed, reopened]

  # the action works on pull request events as well
  pull_request:
    types: [closed, reopened]

jobs:
  build:
    name: "Cross off linked issues"
    runs-on: ubuntu-latest
    steps:
      - name: Cross off any linked issue and PR references
        uses: jonabc/sync-task-issues@f1a0d76ebec6142f13e487525859ba25767adbc2 # v1.2.0
