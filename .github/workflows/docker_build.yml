name: Build docker image and push to GHCR

on:
  schedule:
    - cron: '0 0 * * *'   # Runs at 00:00 UTC every day
  workflow_dispatch:  

permissions:
  contents: read
  packages: write
  attestations: write
  id-token: write

jobs:
  docker-build:
    runs-on: ubuntu-latest
    outputs:
      tagname: ${{ steps.output-tag-name.outputs.tagname }}
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Fetch Repo Name
        id: repo-name
        run: echo "::set-output name=value::$(echo 'gitpod4' | awk -F '/' '{print $2}')"
      - name: Docker meta
        id: meta
        uses: docker/metadata-action@902fa8ec7d6ecbf8d84d538b9b233a880e428804 # v5.7.0
        with:
          # list of Docker images to use as base name for tags
          images: |
            ghcr.io/leanprover-community/${{ github.event.repository.name }}
          # generate Docker tags based on the following events/attributes
          tags: |
            type=ref,event=branch
            type=ref,event=tag
            type=raw,value=latest,enable=${{ github.ref == format('refs/heads/{0}', 'main') }}
      # cached builds according to
      # https://github.com/docker/build-push-action/blob/d17cab8f420a2403aa182827208f8f957ccbf9a9/docs/advanced/cache.md#github-cache
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@b5ca514318bd6ebac0fb2aedd5d36ec1b5c232a2 # v3.10.0
      - name: Login to GitHub Container Registry
        uses: docker/login-action@74a5d142397b4f367a81961eba4e8cd7edddf772 # v3.4.0
        with:
          # with future possibilities to do it to hub.docker.com
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Final tag name
        id: output-tag-name
        run: echo "::set-output name=tagname::${{ steps.meta.outputs.tags }}"
      - name: Build and push image
        id: push
        uses: docker/build-push-action@471d1dc4e07e5cdedd4c2171150001c434f0b7a4 # v6.15.0
        with:
          push: true
          # action can do checkout, but I don't know the exact subdir syntax
          context: .docker/gitpod/
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
      - name: Generate artifact attestation
        uses: actions/attest-build-provenance@c074443f1aee8d4aeeae555aebba3282517141b2 # v2.2.3
        with:
          subject-name: ghcr.io/leanprover-community/${{ github.event.repository.name }}
          subject-digest: ${{ steps.push.outputs.digest }}
          push-to-registry: true
