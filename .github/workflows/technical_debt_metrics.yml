name: Weekly Technical Debt Counters

on:
  schedule:
    - cron: '0 4 * * 1'  # Run at 04:00 UTC every Monday
  workflow_dispatch:

jobs:
  run-script:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      with: # checkout all history so that we can compare across commits
        fetch-depth: 0

    - name: Run script
      id: tech_debt
      run: |
        printf $'summary<<EOF\n%s\nEOF' "$(./scripts/technical-debt-metrics.sh)" >> "$GITHUB_OUTPUT"

    - name: Post output to Zulip
      uses: zulip/github-actions-zulip/send-message@e4c8f27c732ba9bd98ac6be0583096dea82feea5 # v1.0.2
      with:
        api-key: ${{ secrets.ZULIP_API_KEY }}
        email: '<EMAIL>'
        organization-url: 'https://leanprover.zulipchat.com'
        to: 'mathlib4'
        type: 'stream'
        topic: Technical Debt Counters
        content: ${{ steps.tech_debt.outputs.summary }}
