name: Update Mathlib Dependencies

on:
  schedule:
    - cron: '0 * * * *'  # This will run every hour
  workflow_dispatch:

jobs:
  update-dependencies:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0
          token: "${{ secrets.UPDATE_DEPENDENCIES_TOKEN }}"

      - name: Configure Lean
        uses: leanprover/lean-action@f807b338d95de7813c5c50d018f1c23c9b93b4ec # 2025-04-24
        with:
          auto-config: false
          use-github-cache: false
          use-mathlib-cache: false

      - name: Get sha of branch
        id: sha
        run: |
          SHA="$(git rev-parse --verify origin/update-dependencies-bot-use-only)"
          echo "sha=$SHA" >> "$GITHUB_OUTPUT"

      - name: Get PR and labels
        if: ${{ steps.sha.outputs.sha }}
        id: PR # all the steps below are skipped if 'ready-to-merge' is in the list of labels found here
        uses: 8BitJonny/gh-get-current-pr@08e737c57a3a4eb24cec6487664b243b77eb5e36 # 3.0.0
        # TODO: this may not work properly if the same commit is pushed to multiple branches:
        # https://github.com/8BitJonny/gh-get-current-pr/issues/8
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          sha: ${{ steps.sha.outputs.sha }}
          # Only return if PR is still open
          filterOutClosed: true

      - name: Print PR, if found
        run: echo "Found PR ${prNumber} at ${prUrl}"
        if: steps.PR.outputs.pr_found == 'true'
        env:
          prNumber: ${{ steps.PR.outputs.number }}
          prUrl: ${{ steps.PR.outputs.pr_url }}

      - name: Configure Git User
        if: ${{ !contains(steps.PR.outputs.pr_labels, 'ready-to-merge') }}
        run: |
          git config user.name "leanprover-community-mathlib4-bot"
          git config user.email "<EMAIL>"

      - name: Update dependencies
        if: ${{ !contains(steps.PR.outputs.pr_labels, 'ready-to-merge') }}
        run: lake update

      - name: Check if lean-toolchain was modified
        if: ${{ !contains(steps.PR.outputs.pr_labels, 'ready-to-merge') }}
        id: check_toolchain
        run: |
          if git diff --name-only | grep -q "lean-toolchain"; then
            echo "toolchain_modified=true" >> "$GITHUB_OUTPUT"
            echo "Lean toolchain file was modified. Skipping PR creation."
          else
            echo "toolchain_modified=false" >> "$GITHUB_OUTPUT"
          fi

      - name: Generate PR title
        if: ${{ !contains(steps.PR.outputs.pr_labels, 'ready-to-merge') && steps.check_toolchain.outputs.toolchain_modified != 'true' }}
        run: |
          echo "timestamp=$(date -u +"%Y-%m-%d-%H-%M")" >> "$GITHUB_ENV"
          echo "pr_title=chore: update Mathlib dependencies $(date -u +"%Y-%m-%d")" >> "$GITHUB_ENV"

      - name: Create Pull Request
        if: ${{ !contains(steps.PR.outputs.pr_labels, 'ready-to-merge') && steps.check_toolchain.outputs.toolchain_modified != 'true' }}
        uses: peter-evans/create-pull-request@271a8d0340265f705b14b6d32b9829c1cb33d45e # v7.0.8
        with:
          token: "${{ secrets.UPDATE_DEPENDENCIES_TOKEN }}"
          commit-message: "chore: update Mathlib dependencies ${{ env.timestamp }}"
          # this branch is referenced in update_dependencies_zulip.yml
          branch: "update-dependencies-bot-use-only"
          base: master
          title: "${{ env.pr_title }}"
          body: "This PR updates the Mathlib dependencies."
          labels: "auto-merge-after-CI"
