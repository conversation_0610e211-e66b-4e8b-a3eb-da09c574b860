name: Bump lean-toolchain on nightly-testing

on:
  schedule:
    - cron: '0 10/3 * * *'
    # Run every three hours, starting at 11AM CET/2AM PT.
    # This should be 3 hours after lean4 starts building its nightly,
    # and 15 minutes after batteries `nightly-testing` branch bumps its toolchain.
  workflow_dispatch:

jobs:
  update-toolchain:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      with:
        ref: nightly-testing # checkout nightly-testing branch
        token: ${{ secrets.NIGHTLY_TESTING }}

    - name: Get latest release tag from leanprover/lean4-nightly
      id: get-latest-release
      run: |
        RELEASE_TAG="$(curl -s "https://api.github.com/repos/leanprover/lean4-nightly/releases" | jq -r '.[0].tag_name')"
        echo "RELEASE_TAG=$RELEASE_TAG" >> "${GITHUB_ENV}"

    - name: Update lean-toolchain file
      run: |
        echo "leanprover/lean4:${RELEASE_TAG}" > lean-toolchain

    - name: Commit and push changes
      run: |
        git config user.name "leanprover-community-mathlib4-bot"
        git config user.email "<EMAIL>"
        git add lean-toolchain
        # Don't fail if there's nothing to commit
        git commit -m "chore: bump to ${RELEASE_TAG}" || true
        git push origin nightly-testing
