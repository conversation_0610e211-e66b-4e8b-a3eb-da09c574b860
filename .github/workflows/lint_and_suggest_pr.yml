
on:
  pull_request

name: lint and suggest

jobs:
  style_lint:
    if: github.repository == 'leanprover-community/mathlib4' && github.event.pull_request.draft == false
    name: Lint style
    runs-on: ubuntu-latest
    steps:
      - name: cleanup
        run: |
          find . -name . -o -prune -exec rm -rf -- {} +

      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: install Python
        uses: actions/setup-python@a26af69be951a213d495a4c3e4e4022e16d87065 # v5.6.0
        with:
          python-version: 3.8

      - name: Configure Lean
        uses: leanprover/lean-action@f807b338d95de7813c5c50d018f1c23c9b93b4ec # 2025-04-24
        with:
          auto-config: false
          use-github-cache: false
          use-mathlib-cache: false

      # if you update this step (or its dependencies), please also update them in bot_fix_style.yaml
      - name: lint
        run: |
          lake exe lint-style --fix

      - name: suggester / lint-style
        uses: reviewdog/action-suggester@4747dbc9f9e37adba0943e681cc20db466642158 # v1.21.0
        with:
          tool_name: lint-style (comment with "bot fix style" to have the bot commit all style suggestions)

      - name: Install bibtool
        run: |
          sudo apt-get update
          sudo apt-get install -y bibtool

      # if you update this step (or its dependencies), please also update them in bot_fix_style.yaml
      - name: lint references.bib
        run: |
          # ignoring the return code allows the following `reviewdog` step to add GitHub suggestions
          ./scripts/lint-bib.sh || true

      - name: suggester / lint-bib
        uses: reviewdog/action-suggester@4747dbc9f9e37adba0943e681cc20db466642158 # v1.21.0
        with:
          tool_name: lint-bib (comment with "bot fix style" to have the bot commit all style suggestions)

  check_imported:
    if: github.repository == 'leanprover-community/mathlib4' && github.event.pull_request.draft == false
    name: Check all files imported
    runs-on: ubuntu-latest
    steps:
      - name: cleanup
        run: |
          find . -name . -o -prune -exec rm -rf -- {} +

      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Configure Lean
        uses: leanprover/lean-action@f807b338d95de7813c5c50d018f1c23c9b93b4ec # 2025-04-24
        with:
          auto-config: false
          use-github-cache: false
          use-mathlib-cache: false

      # if you update this step (or its dependencies), please also update them in bot_fix_style.yaml
      - name: update {Mathlib, Tactic, Counterexamples, Archive}.lean
        run:
          # ignoring the return code allows the following `reviewdog` step to add GitHub suggestions
          lake exe mk_all || true

      - name: suggester / import list
        uses: reviewdog/action-suggester@4747dbc9f9e37adba0943e681cc20db466642158 # v1.21.0
        with:
          tool_name: imports (comment with "bot fix style" to have the bot commit all style suggestions)
