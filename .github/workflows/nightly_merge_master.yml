# This job merges every commit to `master` into `nightly-testing`, resolving merge conflicts in favor of `nightly-testing`.

name: Merge master to nightly

on:
  schedule:
    - cron: '30 */3 * * *'  # At minute 30 past every 3rd hour.
  workflow_dispatch:

jobs:
  merge-to-nightly:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0
          token: ${{ secrets.NIGHTLY_TESTING }}

      - name: Configure <PERSON>n
        uses: leanprover/lean-action@f807b338d95de7813c5c50d018f1c23c9b93b4ec # 2025-04-24
        with:
          auto-config: false
          use-github-cache: false
          use-mathlib-cache: false

      - name: Configure Git User
        run: |
          git config user.name "leanprover-community-mathlib4-bot"
          git config user.email "<EMAIL>"

      - name: Merge master to nightly favoring nightly changes
        run: |
          git checkout nightly-testing
          # If the merge goes badly, we proceed anyway via '|| true'.
          # CI will report failures on the 'nightly-testing' branch direct to Zulip.
          git merge master --strategy-option ours --no-commit --allow-unrelated-histories || true
          # We aggressively run `lake update`, to avoid having to do this by hand.
          # When Batteries changes break Mathlib, this will likely show up on nightly-testing first.
          lake update
          git add .
          # If there's nothing to do (because there are no new commits from master),
          # that's okay, hence the '|| true'.
          git commit -m "Merge master into nightly-testing" || true
          git push origin nightly-testing
