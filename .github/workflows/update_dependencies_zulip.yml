name: Monitor Dependency Update Failures

on:
  workflow_run:
    workflows: ["continuous integration"]
    types:
      - completed
    branches:
      - 'update-dependencies-**'

jobs:
  monitor-failures:
    runs-on: ubuntu-latest
    if: ${{ github.event.workflow_run.conclusion == 'failure' }}

    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

        with:
          fetch-depth: 2  # Need previous commit for diff

      - name: Set up Python
        uses: actions/setup-python@a26af69be951a213d495a4c3e4e4022e16d87065 # v5.6.0
        with:
          python-version: '3.x'

      - name: Parse dependency changes
        id: parse_changes
        run: |
          output="$(./scripts/parse_lake_manifest_changes.py)"
          echo "Dependency changes:"
          echo "$output"
          delimiter="EOF"
          printf 'changes<<%s\n%s\n%s' "$delimiter" "${message}" "$delimiter"

      - name: Construct message
        uses: actions/github-script@60a0d83039c74a4aee543508d2ffcb1c3799cdea # v7.0.1
        id: construct_message
        with:
          github-token: ${{ secrets.UPDATE_DEPENDENCIES_TOKEN }}
          result-encoding: string
          script: |
            const owner = context.repo.owner, repo = context.repo.repo;
            let output = "❌ `lake update` [failed](" + context.payload.workflow_run.html_url + "). "
            let prs = context.payload.workflow_run.pull_requests
            if (prs.length) {
              for (let pr of prs) {
                const { data: pullRequest } = await github.rest.pulls.get({
                  owner,
                  repo,
                  pull_number: pr.number,
                });
                output += "Found [PR " + pr.number + "](" + pullRequest.html_url + "). "
                await github.rest.issues.removeLabel({
                  owner,
                  repo,
                  issue_number: pr.number,
                  name: "auto-merge-after-CI",
                });
              }
            } else {
              output += "No PR found for this run! If you are feeling impatient and have write access, please go to the following page and click the \"Run workflow\" button!\nhttps://github.com/leanprover-community/mathlib4/actions/workflows/update_dependencies.yml";
            }
            output += "\n\n" + "${{ steps.parse_changes.outputs.changes }}";
            return output;

      - name: Send Zulip message
        uses: zulip/github-actions-zulip/send-message@e4c8f27c732ba9bd98ac6be0583096dea82feea5 # v1.0.2
        with:
          api-key: ${{ secrets.ZULIP_API_KEY }}
          email: '<EMAIL>'
          organization-url: 'https://leanprover.zulipchat.com'
          to: 'mathlib reviewers'
          type: 'stream'
          topic: 'Mathlib `lake update` failure'
          content: |
            ${{ steps.construct_message.outputs.result }}

  monitor-success:
    runs-on: ubuntu-latest
    if: ${{ github.event.workflow_run.conclusion == 'success' }}

    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 2  # Need previous commit for diff

      - name: Set up Python
        uses: actions/setup-python@a26af69be951a213d495a4c3e4e4022e16d87065 # v5.6.0
        with:
          python-version: '3.x'

      - name: Parse dependency changes
        id: parse_changes
        run: |
          output="$(./scripts/parse_lake_manifest_changes.py)"
          echo "Dependency changes:"
          echo "$output"
          delimiter="EOF"
          printf 'changes<<%s\n%s\n%s' "$delimiter" "${message}" "$delimiter"

      - name: Construct success message
        id: construct_message
        uses: actions/github-script@60a0d83039c74a4aee543508d2ffcb1c3799cdea # v7.0.1
        with:
          github-token: ${{ secrets.UPDATE_DEPENDENCIES_TOKEN }}
          result-encoding: string
          script: |
            const owner = context.repo.owner, repo = context.repo.repo;
            let output = "✅ `lake update` succeeded! "
            let prs = context.payload.workflow_run.pull_requests
            if (prs.length) {
              for (let pr of prs) {
                output += "merged via #" + pr.number + ". ";
              }
              output += "\n\n" + "${{ steps.parse_changes.outputs.changes }}";
              return output
            }
            return ''  // Return empty string if no PRs found

      - name: Send Zulip message
        if: ${{ steps.construct_message.outputs.result != '' }}
        uses: zulip/github-actions-zulip/send-message@e4c8f27c732ba9bd98ac6be0583096dea82feea5 # v1.0.2
        with:
          api-key: ${{ secrets.ZULIP_API_KEY }}
          email: '<EMAIL>'
          organization-url: 'https://leanprover.zulipchat.com'
          to: 'mathlib reviewers'
          type: 'stream'
          topic: 'Mathlib `lake update` success'
          content: |
            ${{ steps.construct_message.outputs.result }}
