// 经验：
// 1. mathlib4 中 Collinear 的正确用法是直接 import Mathlib.Geometry.Euclidean.Angle.Unoriented.Affine 并用 Collinear ℝ ({A, B, C} : Set Plane)。
// 2. 欧几里得平面 Plane 类型应为 EuclideanSpace ℝ (Fin 2)，Sphere 类型参数为 Plane。
// 7. EuclideanGeometry.angle 在 Mathlib.Geometry.Euclidean.Angle.Unoriented.Affine 中定义，需 import 该文件并用全名 EuclideanGeometry.angle。
// 8. Lean 4 变量声明用 variable，避免 variables。
// 9. theorem 参数全部显式写出可避免 Lean 4 推断失败。
// 11. 使用 EuclideanGeometry.angle 必须 import Mathlib.Geometry.Euclidean.Angle.Unoriented.Affine。
// 12. 变量声明用 variable，避免 variables。
// 13. theorem 参数全部显式写出可避免 Lean 4 推断失败。
// 14. 使用 EuclideanSpace 作为二维欧氏空间类型时，需 import Mathlib.Analysis.InnerProductSpace.PiL2。
// 15. 这样可自动获得 dist、NormedAddTorsor 等实例，避免类型推断失败。
// 16. mathlib4 中无 unorientedAngle，应用 InnerProductGeometry.angle 替代。
