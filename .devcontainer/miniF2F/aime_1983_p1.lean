import Mathlib.Analysis.SpecialFunctions.Log.Basic
import Mathlib.Data.Real.Basic
import Mathlib.Tactic

-- AIME 1983 Problem 1
-- Given log_x w = 24, log_y w = 40 and log_{xyz} w = 12 with x, y, z > 1, determine log_z w.

theorem aime_1983_p1 (x y z w : ℝ) (hx : x > 1) (hy : y > 1) (hz : z > 1)
  (h1 : Real.log w / Real.log x = 24)
  (h2 : Real.log w / Real.log y = 40)
  (h3 : Real.log w / Real.log (x * y * z) = 12) :
  Real.log w / Real.log z = 60 := by
  -- Convert logarithmic equations to natural logarithm form
  have eq1 : Real.log w = 24 * Real.log x := by
    rw [div_eq_iff] at h1
    · exact h1
    · exact Real.log_ne_zero.mpr ⟨ne_of_gt (zero_lt_one.trans hx), ne_of_gt hx, ne_of_gt (neg_one_lt_zero.trans (zero_lt_one.trans hx))⟩
  have eq2 : Real.log w = 40 * Real.log y := by
    rw [div_eq_iff] at h2
    · exact h2
    · exact Real.log_ne_zero.mpr ⟨ne_of_gt (zero_lt_one.trans hy), ne_of_gt hy, ne_of_gt (neg_one_lt_zero.trans (zero_lt_one.trans hy))⟩
  have eq3 : Real.log w = 12 * Real.log (x * y * z) := by
    rw [div_eq_iff] at h3
    · exact h3
    · have hxyz_pos : 0 < x * y * z := by
        apply mul_pos
        · apply mul_pos
          · exact zero_lt_one.trans hx
          · exact zero_lt_one.trans hy
        · exact zero_lt_one.trans hz
      have hxyz_ne_one : x * y * z ≠ 1 := by
        have : 1 < x * y * z := by
          have hxy : 1 < x * y := one_lt_mul_of_le_of_lt hx.le hy
          exact one_lt_mul_of_le_of_lt hxy.le hz
        exact ne_of_gt this
      exact Real.log_ne_zero.mpr ⟨ne_of_gt hxyz_pos, hxyz_ne_one, ne_of_gt (neg_one_lt_zero.trans hxyz_pos)⟩
  -- Establish relationship between ln x and ln y
  have rel_xy : Real.log y = (3/5) * Real.log x := by
    -- From eq1 and eq2: 24 * Real.log x = 40 * Real.log y
    have h_eq : 24 * Real.log x = 40 * Real.log y := by
      rw [← eq1, ← eq2]
    -- Solve for Real.log y: 40 * Real.log y = 24 * Real.log x
    -- Therefore Real.log y = (24/40) * Real.log x = (3/5) * Real.log x
    have h_40_ne_zero : (40 : ℝ) ≠ 0 := by norm_num
    have h_24_ne_zero : (24 : ℝ) ≠ 0 := by norm_num
    -- From h_eq: 24 * Real.log x = 40 * Real.log y
    -- Divide both sides by 40: (24/40) * Real.log x = Real.log y
    have : Real.log y = (24 / 40) * Real.log x := by
      field_simp at h_eq ⊢
      linarith
    rw [this]
    norm_num
  -- Find relationship between ln x and ln z
  have rel_xz : Real.log z = (2/5) * Real.log x := by
    -- From eq3: Real.log w = 12 * Real.log (x * y * z)
    -- Expand: Real.log w = 12 * (Real.log x + Real.log y + Real.log z)
    have h_expand : Real.log w = 12 * (Real.log x + Real.log y + Real.log z) := by
      rw [eq3]
      rw [Real.log_mul (ne_of_gt (mul_pos (zero_lt_one.trans hx) (zero_lt_one.trans hy))) (ne_of_gt (zero_lt_one.trans hz))]
      rw [Real.log_mul (ne_of_gt (zero_lt_one.trans hx)) (ne_of_gt (zero_lt_one.trans hy))]
    -- From eq1: Real.log w = 24 * Real.log x
    -- So: 24 * Real.log x = 12 * (Real.log x + Real.log y + Real.log z)
    have h_eq_expand : 24 * Real.log x = 12 * (Real.log x + Real.log y + Real.log z) := by
      rw [← eq1, ← h_expand]
    -- Simplify: 2 * Real.log x = Real.log x + Real.log y + Real.log z
    have h_simp : 2 * Real.log x = Real.log x + Real.log y + Real.log z := by
      have h_12_ne_zero : (12 : ℝ) ≠ 0 := by norm_num
      field_simp at h_eq_expand ⊢
      linarith
    -- Therefore: Real.log z = Real.log x - Real.log y
    have h_z_eq : Real.log z = Real.log x - Real.log y := by
      linarith [h_simp]
    -- Substitute rel_xy: Real.log z = Real.log x - (3/5) * Real.log x = (2/5) * Real.log x
    rw [h_z_eq, rel_xy]
    ring
  -- Calculate final result
  -- log_z w = ln w / ln z = (24 * ln x) / ((2/5) * ln x) = 24 / (2/5) = 24 * (5/2) = 60
  rw [div_eq_iff]
  · rw [eq1, rel_xz]
    field_simp
    ring
  · exact Real.log_ne_zero.mpr ⟨ne_of_gt (zero_lt_one.trans hz), ne_of_gt hz, ne_of_gt (neg_one_lt_zero.trans (zero_lt_one.trans hz))⟩
