import Mathlib.Analysis.Convex.Jensen
import Mathlib.Analysis.SpecialFunctions.Trigonometric.Basic
import Mathlib.Analysis.SpecialFunctions.Trigonometric.Bounds
import Mathlib.Data.Real.Basic

open Real

theorem acute_triangle_inequality {A B C : ℝ}
  (hA : 0 < A) (hB : 0 < B) (hC : 0 < C)
  (hA_lt_pi2 : A < π / 2) (hB_lt_pi2 : B < π / 2) (hC_lt_pi2 : C < π / 2)
  (h_sum : A + B + C = π) :
  cos A + cos B + cos C + (sqrt 3 / 3) * (sin A + sin B + sin C) ≤ 3 :=
by
  -- Step 1: Express the left-hand side in a simplified form
  have h1 : ∀ X, cos X + (sqrt 3 / 3) * sin X = (2 / sqrt 3) * cos (X - π / 6),
  { intro X,
    rw [cos_sub, sin_sub],
    have : cos (π / 6) = sqrt 3 / 2 := by norm_num,
    have : sin (π / 6) = 1 / 2 := by norm_num,
    rw [this, this],
    ring },
  
  -- Applying the above identity to A, B, and C
  have h2 : cos A + (sqrt 3 / 3) * sin A + cos B + (sqrt 3 / 3) * sin B + cos C + (sqrt 3 / 3) * sin C
          = (2 / sqrt 3) * (cos (A - π / 6) + cos (B - π / 6) + cos (C - π / 6)),
  { rw [h1 A, h1 B, h1 C],
    ring },
  
  -- Step 2: Define new variables
  let α := A - π / 6,
  let β := B - π / 6,
  let γ := C - π / 6,
  
  -- Establish the sum of new variables
  have h_sum_new : α + β + γ = π / 2,
  { rw [α, β, γ, h_sum],
    ring },
  
  -- Step 3: Apply Jensen's Inequality
  -- Define the function f(x) = cos x
  let f := cos,
  
  -- Show that f is concave on the interval (-π/6, π/3)
  have h_concave : ConcaveOn ℝ (Icc (-π / 6) (π / 3)) f,
  { apply concaveOn_of_deriv2_nonpos,
    { intros x hx,
      simp [cos],
      norm_num },
    { intros x hx,
      simp [cos],
      norm_num } },
  
  -- Apply Jensen's inequality
  have h_jensen : f α + f β + f γ ≤ 3 * f ((α + β + γ) / 3),
  { apply h_concave.map_sum_le,
    { intros i hi,
      simp,
      linarith },
    { simp,
      linarith },
    { intros i hi,
      simp,
      linarith } },
  
  -- Simplify the right-hand side
  have h_rhs : f ((α + β + γ) / 3) = f (π / 6),
  { rw [h_sum_new],
    norm_num },
  
  -- Compute f(π/6)
  have h_f_pi6 : f (π / 6) = sqrt 3 / 2 := by norm_num,
  
  -- Combine the results
  have h3 : f α + f β + f γ ≤ 3 * (sqrt 3 / 2),
  { rw [h_rhs, h_f_pi6] at h_jensen,
    exact h_jensen },
  
  -- Step 4: Conclude the proof
  have h4 : (2 / sqrt 3) * (f α + f β + f γ) ≤ (2 / sqrt 3) * (3 * (sqrt 3 / 2)),
  { apply mul_le_mul_of_nonneg_left h3,
    norm_num },
  
  -- Simplify the right-hand side
  have h_rhs_simplified : (2 / sqrt 3) * (3 * (sqrt 3 / 2)) = 3,
  { norm_num },
  
  -- Final inequality
  rw [h2, h_rhs_simplified] at h4,
  exact h4