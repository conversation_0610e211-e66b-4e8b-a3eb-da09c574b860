import Mathlib.NumberTheory.QuadraticReciprocity
import Mathlib.NumberTheory.LegendreSymbol.QuadraticChar

open Nat

-- The quadratic reciprocity theorem
theorem quadratic_reciprocity {p q : ℕ} (hp : Prime p) (hq : Prime q) (hp_odd : p ≠ 2) (hq_odd : q ≠ 2) (hneq : p ≠ q) :
  legendreSym p q * legendreSym q p = (-1) ^ ((p - 1) / 2 * (q - 1) / 2) :=
begin
  -- Since p and q are distinct odd primes, we can apply the quadratic reciprocity theorem
  have hqr := legendreSym.quadratic_reciprocity hp hq hneq,
  -- The theorem states that (p/q) * (q/p) = (-1) ^ ((p - 1) / 2 * (q - 1) / 2)
  rw hqr,
end