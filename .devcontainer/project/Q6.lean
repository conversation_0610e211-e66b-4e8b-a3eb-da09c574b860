import Mathlib.GroupTheory.Sylow
import Mathlib.Data.Nat.Prime

open Nat

theorem exists_subgroup_of_order_p_pow {G : Type*} [Group G] [Fintype G]
    {p k : ℕ} (hp : p.Prime) (hpk : p ^ k ∣ Fintype.card G) :
    ∃ H : Subgroup G, Fintype.card H = p ^ k := by
  -- Apply the generalization of <PERSON><PERSON><PERSON>'s first theorem
  obtain ⟨H, hH⟩ := Sylow.exists_subgroup_card_pow_prime_of_le_card hp
    (Sylow.isPGroup_of_dvd_card hp hpk) hpk
  exact ⟨H, hH⟩