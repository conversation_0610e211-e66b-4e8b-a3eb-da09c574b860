/-
Lean 4 + Math<PERSON>b 4.20.0 proof for:
Let the sequence {aₙ} be defined by a₁ = 1, and a_{n+1} = aₙ + 1/aₙ.
Prove: For all n ≥ 2, aₙ > √(2n-1).
-/

import Mathlib.Data.Real.Basic
import Mathlib.Tactic
import Mathlib.Data.Nat.Basic
import Mathlib.Analysis.SpecialFunctions.Pow.Real
import Mathlib.Analysis.MeanInequalities

open Real

/-- The sequence a : ℕ → ℝ, recursively defined by:
    a 1 = 1
    a (n+1) = a n + 1 / a n
-/
noncomputable def a : ℕ → ℝ
| 0     => 0
| 1     => 1
| n+2   => a (n+1) + 1 / a (n+1)

-- Helper lemma: a_n is positive for n ≥ 1.
lemma an_pos (n : ℕ) (hn : 1 ≤ n) : 0 < a n := by
  induction n using Nat.strong_induction_on with
  | h n ih =>
    rcases hn.eq_or_lt with rfl | hn_gt_1
    · simp [a] -- Base case: n = 1
    · -- Inductive step: n > 1, so n ≥ 2
      have h_n_ge_2 : 2 ≤ n := by l<PERSON>rith
      have an_def : a n = a (n-1) + 1 / a (n-1) := by
        let k := n - 2
        have : n = k + 2 := (Nat.sub_add_cancel h_n_ge_2).symm
        rw [this, a]; simp
      rw [an_def]
      have h_nm1_ge_1 : 1 ≤ n - 1 := by omega
      have ne_zero : n ≠ 0 := by linarith
      have ih' : 0 < a (n-1) := ih (n-1) (Nat.pred_lt ne_zero) h_nm1_ge_1
      exact add_pos ih' (one_div_pos.mpr ih')

/-- For all n ≥ 2, a n ^ 2 > 2 * n - 1. -/
lemma an_sq_gt_2n_sub_1 {n : ℕ} (hn : 2 ≤ n) : a n ^ 2 > 2 * (n : ℝ) - 1 := by
  induction n using Nat.strong_induction_on with
  | h n ih =>
    rcases hn.eq_or_lt with rfl | hn_gt_2
    · -- Base case: n = 2, a_2 = 2, 2^2 = 4 > 3 = 2*2-1
      simp [a, sq]; norm_num
    · -- Inductive step: n > 2
      have h_nm1_ge_2 : 2 ≤ n - 1 := by omega
      have ne_zero : n ≠ 0 := by linarith
      have ih' : a (n - 1) ^ 2 > 2 * (↑(n - 1) : ℝ) - 1 :=
        ih (n - 1) (Nat.pred_lt ne_zero) h_nm1_ge_2

      have an_def : a n = a (n - 1) + 1 / a (n - 1) := by
        let k := n - 2
        have : n = k + 2 := (Nat.sub_add_cancel (Nat.le_of_lt hn_gt_2)).symm
        rw [this, a]; simp
      have anm1_pos : 0 < a (n-1) := an_pos (n-1) (by linarith)

      -- Use AM-GM: for a > 0, (a + 1/a)^2 ≥ 4 by AM-GM inequality
      -- But we need a more precise bound using the inductive hypothesis
      rw [an_def, sq]

      -- Apply AM-GM to get a + 1/a ≥ 2, so (a + 1/a)^2 ≥ 4
      -- But we can be more precise: (a + 1/a)^2 = a^2 + 2 + 1/a^2 ≥ a^2 + 2
      have h_amgm : a (n - 1) + 1 / a (n - 1) ≥ 2 := by
        -- Use AM-GM: p₁^w₁ * p₂^w₂ ≤ w₁ * p₁ + w₂ * p₂ with w₁ = w₂ = 1/2, p₁ = a, p₂ = 1/a
        have h_geom_arith := Real.geom_mean_le_arith_mean2_weighted
          (by norm_num : (0 : ℝ) ≤ 1/2) (by norm_num : (0 : ℝ) ≤ 1/2)
          (le_of_lt anm1_pos) (one_div_nonneg.mpr (le_of_lt anm1_pos))
          (by norm_num : (1/2 : ℝ) + 1/2 = 1)
        -- This gives a^(1/2) * (1/a)^(1/2) ≤ (1/2) * a + (1/2) * (1/a)
        -- Since a^(1/2) * (1/a)^(1/2) = 1, we get 1 ≤ (a + 1/a)/2, so a + 1/a ≥ 2
        have h_sqrt_eq : a (n - 1) ^ (1/2 : ℝ) * (1 / a (n - 1)) ^ (1/2 : ℝ) = 1 := by
          rw [← mul_rpow (le_of_lt anm1_pos) (one_div_nonneg.mpr (le_of_lt anm1_pos))]
          simp [anm1_pos.ne']
        rw [h_sqrt_eq] at h_geom_arith
        linarith [h_geom_arith]

      -- Now (a + 1/a)^2 ≥ 4, but we need to use the inductive hypothesis more carefully
      have h_bound : (a (n - 1) + 1 / a (n - 1)) ^ 2 ≥ a (n - 1) ^ 2 + 2 := by
        have h_expand : (a (n - 1) + 1 / a (n - 1)) ^ 2 =
                        a (n - 1) ^ 2 + 2 * a (n - 1) * (1 / a (n - 1)) + (1 / a (n - 1)) ^ 2 := by ring
        rw [h_expand]
        have h_mul_eq : 2 * a (n - 1) * (1 / a (n - 1)) = 2 := by field_simp [anm1_pos.ne']
        rw [h_mul_eq]
        have h_sq_nonneg : (1 / a (n - 1)) ^ 2 ≥ 0 := sq_nonneg _
        linarith [h_sq_nonneg]

      -- Since a_{n-1}^2 > 2(n-1) - 1, we get a_n^2 ≥ 2(n-1) - 1 + 2 = 2n - 1
      -- But we need strict inequality, which comes from the positive term (1/a)^2
      have h_strict : (a (n - 1) + 1 / a (n - 1)) ^ 2 > 2 * (n : ℝ) - 1 := by
        -- We know a_{n-1}^2 > 2(n-1) - 1, so a_{n-1}^2 + 2 > 2(n-1) - 1 + 2 = 2n - 1
        have h1 : a (n - 1) ^ 2 > 2 * (n - 1 : ℝ) - 1 := by
          have h_cast : (↑(n - 1) : ℝ) = (↑n - 1 : ℝ) := by
            rw [Nat.cast_sub (Nat.one_le_of_lt hn_gt_2)]
            simp
          rw [← h_cast]
          exact ih'
        have h2 : 2 * (n - 1 : ℝ) - 1 + 2 = 2 * (n : ℝ) - 1 := by
          have : (n : ℝ) = (n - 1 : ℝ) + 1 := by simp [Nat.cast_sub (le_of_lt hn_gt_2)]
          rw [this]; ring
        have h3 : a (n - 1) ^ 2 + 2 > 2 * (n : ℝ) - 1 := by
          have : a (n - 1) ^ 2 + 2 > 2 * (n - 1 : ℝ) - 1 + 2 := by linarith [h1]
          rwa [h2] at this
        -- Since (a + 1/a)^2 ≥ a^2 + 2, we get the result
        exact lt_of_lt_of_le h3 h_bound
      simp only [sq] at h_strict
      exact h_strict

/-- For all n ≥ 2, we have a n > sqrt (2 * n - 1). -/
theorem a_n_gt_sqrt_2n_sub_1 {n : ℕ} (hn : 2 ≤ n) : a n > sqrt (2 * n - 1) := by
  have an_pos' : 0 < a n := an_pos n (by linarith)
  have rhs_nonneg : 0 ≤ 2 * (n:ℝ) - 1 := by linarith [(show (2:ℝ) ≤ n from Nat.cast_le.mpr hn)]
  have h_sq : a n ^ 2 > 2 * (n:ℝ) - 1 := an_sq_gt_2n_sub_1 hn
  have h_sqrt_lt : sqrt (2 * (n:ℝ) - 1) < sqrt (a n ^ 2) := Real.sqrt_lt_sqrt rhs_nonneg h_sq
  rwa [Real.sqrt_sq an_pos'.le] at h_sqrt_lt

-- For reference: when n = 1, a₁ = 1 = sqrt(1), so the strict inequality fails only for n=1.
-- #eval a 5 -- Should output something > sqrt(9) = 3
-- #eval (a 5 > sqrt (2 * 5 - 1)) -- true
