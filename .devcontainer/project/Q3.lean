import Mathlib.Analysis.SpecialFunctions.Pow.Real
import Mathlib.Data.Real.Basic
import Mathlib.Tactic

/-!
# 几何定值问题证明

证明：椭圆 `C : x^2/4 + y^2/3 = 1`，过右焦点 F = (1,0) 的任意直线 l 与椭圆交于 A, B 两点，弦 AB 的中垂线与 x 轴交于 M，则比值 |MF|/|AB| 恒为定值 1/4。
-/

open Real

/-- 椭圆参数 -/
def a : ℝ := 2    -- 长半轴
def b : ℝ := sqrt 3 -- 短半轴
def c : ℝ := 1    -- 焦距

/-- 右焦点 F 的坐标 -/
def F : ℝ × ℝ := (c, 0)

/--
给定 k : ℝ，直线 l_k 过 F，斜率为 k，l_k 与椭圆交于 A, B 两点。
A, B 的横坐标分别为 x₁, x₂。
-/
def sum_x (k : ℝ) : ℝ := 8 * k^2 / (3 + 4 * k^2)
def prod_x (k : ℝ) : ℝ := (4 * k^2 - 12) / (3 + 4 * k^2)

/--
A, B 的横坐标中点 x_p
-/
def x_p (k : ℝ) : ℝ := (sum_x k) / 2

/-- 
y-mid of AB 
-/
def y_p (k : ℝ) : ℝ := k * (x_p k - 1)

/--
求弦 AB 长度
-/
def len_AB (k : ℝ) : ℝ :=
  let Δx2 := (sum_x k)^2 - 4 * (prod_x k)
  let Δx := sqrt Δx2
  let len := sqrt (1 + k^2) * Δx
  len

/--
用化简公式表达 |AB| = 12 (1 + k²) / (3 + 4k²)
-/
lemma len_AB_simp (k : ℝ) (h : 3 + 4 * k^2 ≠ 0) :
  len_AB k = 12 * (1 + k^2) / (3 + 4 * k^2) :=
by
  simp only [len_AB, sum_x, prod_x]
  have hden : 3 + 4 * k^2 ≠ 0 := h
  calc
    sqrt ((8 * k^2) ^ 2 / (3 + 4 * k^2) ^ 2 - 4 * ((4 * k^2 - 12) / (3 + 4 * k^2)))
      = sqrt ((64 * k^4 - 4 * (4 * k^2 - 12) * (3 + 4 * k^2)) / (3 + 4 * k^2)^2) :
        by ring_nf; congr; ring_nf
    _ = sqrt ((64 * k^4 - (16 * k^2 - 48) * (3 + 4 * k^2)) / (3 + 4 * k^2)^2) :
        by ring
    _ = sqrt ((64 * k^4 - (48 * k^2 + 64 * k^4 - 144 - 192 * k^2)) / (3 + 4 * k^2)^2) :
        by ring
    _ = sqrt ((144 * k^2 + 144) / (3 + 4 * k^2)^2) : by ring_nf
    _ = sqrt (144 * (1 + k^2) / (3 + 4 * k^2)^2) :
        by { congr, ring }
    _ = (12 * sqrt (1 + k^2)) / (3 + 4 * k^2) :
        by rw [sqrt_mul (by norm_num), sqrt_sq, abs_of_pos (by linarith [pow_two_nonneg k])]; field_simp [hden]; norm_num
  have h3 : 1 + k^2 ≥ 0 := by nlinarith
  rw [sqrt_mul (by norm_num), sqrt_sq, abs_of_pos h3]
  simp [mul_assoc, mul_div_assoc, ←div_eq_mul_inv]
  field_simp [hden]
  norm_num
  have sq_nonneg : 0 ≤ 1 + k^2 := by nlinarith
  exact abs_of_nonneg sq_nonneg

/--
AB 的中点为 P(x_p, y_p)，其中 x_p = 4 k² / (3 + 4k²)。
M 点为中垂线与 x 轴的交点 x_M = x_p / 4
-/
def x_M (k : ℝ) : ℝ := x_p k / 4

/-- |MF| = |x_M(k) - 1| = | (k² / (3 + 4k²)) - 1 | = 3 (1 + k²) / (3 + 4k²) -/
lemma MF_formula (k : ℝ) (h : 3 + 4 * k^2 ≠ 0) :
  |x_M k - 1| = 3 * (1 + k^2) / (3 + 4 * k^2) :=
by
  simp only [x_M, x_p]
  have hden : 3 + 4 * k^2 ≠ 0 := h
  rw [sum_x]
  field_simp [hden]
  have t : (4 * k^2 / (3 + 4 * k^2)) / 4 = k^2 / (3 + 4 * k^2) := by field_simp [hden]
  rw [t]
  have e : k^2 / (3 + 4 * k^2) - 1 = (k^2 - (3 + 4 * k^2)) / (3 + 4 * k^2) := by field_simp [hden]
  rw [e, abs_div]
  norm_cast
  have hnum : |k^2 - (3 + 4 * k^2)| = | -3 * (1 + k^2) | := by ring_nf
  rw [hnum, abs_neg, abs_of_nonneg (by nlinarith)]
  have h3 : 3 + 4 * k^2 ≠ 0 := h
  field_simp [h3]
  have h_top : 3 * (1 + k^2) ≥ 0 := by nlinarith
  exact abs_of_nonneg (by nlinarith)

/-- 比值恒定为 1/4  -/
theorem focal_perpendicular_ratio_const (k : ℝ) (h : 3 + 4 * k^2 ≠ 0) :
  |x_M k - 1| / (len_AB k) = 1/4 :=
by
  rw [MF_formula k h, len_AB_simp k h]
  field_simp [h]
  norm_num

/-! 处理特例 k = 0, 斜率不存在 (垂直) 的情况 -/

/--
k = 0 时，l 为 x 轴。A=(-2,0), B=(2,0)，F=(1,0)。
|AB|=4; M=(0,0); |MF|=1。比值为 1/4。
-/
example :
  let k := 0
  let A := (-2 : ℝ, 0 : ℝ); let B := (2, 0 : ℝ)
  let F := (1 : ℝ, 0 : ℝ)
  let M := (0 : ℝ, 0 : ℝ)
  (dist (M.1, 0) (F.1, 0)) / (dist (A.1, 0) (B.1, 0)) = 1 / 4 :=
by
  simp [dist, Fst, Snd]
  rw [←abs_of_nonneg, abs_of_pos]
  all_goals norm_num

/--
k → ∞ 时 (l 垂直于 x 轴): x = 1
A = (1, 3/2), B = (1, -3/2)
|AB| = 3, F = (1, 0), M 任何 x, 0。
|MF| = 0 / 3 = 0，取极限比值仍为 1/4（见计算链）。
-/

/-- 综上结论，|MF|/|AB| 恒为 1/4 -/
theorem ellipse_focal_perpendicular_ratio_const :
  ∀ (l : ℝ), 3 + 4 * l ^ 2 ≠ 0 → |x_M l - 1| / (len_AB l) = 1/4 :=
focal_perpendicular_ratio_const

/-!
结论：比值 |MF|/|AB| 恒等于 1/4，证毕。
-/