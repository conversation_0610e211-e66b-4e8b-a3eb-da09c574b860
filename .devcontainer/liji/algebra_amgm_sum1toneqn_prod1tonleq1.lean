-- Proof content:
-- 1. [Problem Restatement] Given non–negative reals a₁,…,aₙ with a₁+⋯+aₙ = n, prove that a₁a₂⋯aₙ ≤ 1. 2. [Key Idea] Apply the arithmetic–mean ≥ geometric–mean (AM–GM) inequality; equality forces every aᵢ to be 1. 3. [Proof] Proof 1 (AM–GM). By AM–GM, (a₁+⋯+aₙ)/n ≥ (a₁a₂⋯aₙ)^{1/n}. The left side equals n/n = 1, so (a₁a₂⋯aₙ)^{1/n} ≤ 1, hence a₁a₂⋯aₙ ≤ 1ⁿ = 1. Proof 2 (<PERSON> on ln). Because ln x is concave on (0,∞), <PERSON>’s inequality gives (ln a₁+⋯+ln aₙ)/n ≤ ln[(a₁+⋯+aₙ)/n] = ln 1 = 0. Exponentiating yields a₁a₂⋯aₙ ≤ 1. (If some aᵢ = 0, the product is 0 ≤ 1, so the statement still holds.) 4. [Conclusion] With sum fixed at n, the maximum possible product is 1, achieved only when every aᵢ equals 1.
