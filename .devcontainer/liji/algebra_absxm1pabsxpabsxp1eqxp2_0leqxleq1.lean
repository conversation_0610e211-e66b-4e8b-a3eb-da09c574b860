-- Proof content:
-- 1. [Problem Restatement] Prove that the equation |x − 1| + |x| + |x + 1| = x + 2 forces the real number x to lie in the interval 0 ≤ x ≤ 1. 2. [Key Idea] Apply the triangle inequality to bound the left‐hand side from below and examine when equality can occur. 3. [Proof] Method 1 (Triangle-inequality squeeze) a. By the triangle inequality, |x − 1| + |x + 1| ≥ |(x − 1) − (x + 1)| = 2. Hence |x − 1| + |x| + |x + 1| ≥ |x| + 2. (1) b. The hypothesis gives equality: |x − 1| + |x| + |x + 1| = x + 2. Combining with (1) yields |x| + 2 = x + 2 ⇒ |x| = x ⇒ x ≥ 0. (2) c. Equality in (1) occurs precisely when the vectors x − 1 and −(x + 1) point in the same direction, i.e. when (x − 1)(x + 1) ≤ 0 ⇔ −1 ≤ x ≤ 1. (3) d. Intersecting (2) and (3) gives 0 ≤ x ≤ 1, as required. Method 2 (Direct piecewise check) Split ℝ into the four intervals determined by −1, 0, 1 and verify that the equation fails everywhere except on [0,1], where a direct substitution shows it holds identically. 4. [Conclusion] Therefore the given absolute-value identity holds exactly for 0 ≤ x ≤ 1.
